// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'بنك الموظفين';

  @override
  String get appSubtitle => 'نظام إدارة الموظفين المصرفي';

  @override
  String get loginTitle => 'تسجيل الدخول';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get loginWithBiometric => 'تسجيل الدخول بالبصمة';

  @override
  String get rememberMe => 'تذكرني';

  @override
  String get uncheckrememberme => 'تم إلغاء تذكر بيانات تسجيل الدخول لأن البصمة مفعلة ';

  @override
  String get enteremail => 'أدخل البريد الإلكتروني';

  @override
  String get validemail => 'يرجى إدخال بريد إلكتروني صحيح';

  @override
  String get enterpassword => 'أدخل كلمة المرور';

  @override
  String get correctpassword => 'كلمة المرور يجب أن تحتوي على 6 أحرف على الأقل';

  @override
  String get securityInformation => 'معلومات الأمان';

  @override
  String get rememberMeInfo => 'عند تفعيل هذا الخيار، سيتم حفظ بيانات تسجيل الدخول بشكل آمن على جهازك لتسهيل الدخول في المرات القادمة.\n\nيمكنك إلغاء تفعيل هذا الخيار في أي وقت من إعدادات التطبيق.';

  @override
  String get understood => 'فهمت';

  @override
  String get loginError => 'حدث خطأ في تسجيل الدخول';

  @override
  String get invalidCredentials => 'بيانات تسجيل الدخول غير صحيحة';

  @override
  String get employeeInfo => 'معلومات الموظف';

  @override
  String get nationalNumber => 'الرقم الوطني';

  @override
  String get connectedWithCompany => 'مرتبط بالشركة';

  @override
  String get managerName => 'اسم المدير';

  @override
  String get workSchedule => 'جدول العمل';

  @override
  String get refreshData => 'تحديث البيانات';

  @override
  String get hiring => 'تعيين';

  @override
  String get identification => 'الرقم الوطني';

  @override
  String get directmanager => 'المدير المباشر';

  @override
  String get quickactions => 'إجراءات سريعة';

  @override
  String get myprofile => 'الملف الشخصي';

  @override
  String get leaveTypes => 'أنواع الإجازات';

  @override
  String get myLeaveRequests => 'طلباتي';

  @override
  String get leaveApprovals => 'الموافقات';

  @override
  String get settings => 'الإعدادات';

  @override
  String get appSettings => 'إعدادات التطبيق';

  @override
  String get customizeSettings => 'قم بتخصيص إعدادات التطبيق حسب احتياجاتك';

  @override
  String get accountSettings => 'إعدادات الحساب';

  @override
  String get changePassword => 'تغيير كلمة المرور';

  @override
  String get changePasswordSubtitle => 'قم بتحديث كلمة المرور الخاصة بك';

  @override
  String get securitySettings => 'إعدادات الأمان';

  @override
  String get biometricLogin => 'تسجيل الدخول بالبصمة';

  @override
  String get biometricEnabledSubtitle => 'مفعل - يمكنك تسجيل الدخول بالبصمة';

  @override
  String get biometricDisabledSubtitle => 'غير مفعل - استخدم كلمة المرور فقط';

  @override
  String get biometricUpdating => 'جاري التحديث...';

  @override
  String get appearanceSettings => 'إعدادات المظهر';

  @override
  String get changeLanguage => 'تغيير اللغة';

  @override
  String get changeLanguageSubtitle => 'اختر لغة التطبيق المفضلة لديك';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get languages => 'اللغات';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'الإنجليزية';

  @override
  String get french => 'الفرنسية';

  @override
  String get currentPassword => 'كلمة المرور الحالية';

  @override
  String get newPassword => 'كلمة المرور الجديدة';

  @override
  String get confirmNewPassword => 'تأكيد كلمة المرور الجديدة';

  @override
  String get updatePassword => 'تحديث كلمة المرور';

  @override
  String get passwordUpdated => 'تم تحديث كلمة المرور بنجاح';

  @override
  String get passwordUpdateFailed => 'فشل في تحديث كلمة المرور';

  @override
  String get biometricEnabled => 'تم تفعيل تسجيل الدخول بالبصمة بنجاح';

  @override
  String get biometricDisabled => 'تم إلغاء تفعيل تسجيل الدخول بالبصمة';

  @override
  String get biometricError => 'حدث خطأ أثناء تحديث إعدادات البصمة';

  @override
  String get biometricSetup => 'إعداد تسجيل الدخول بالبصمة';

  @override
  String get biometricSetupMessage => 'لتفعيل تسجيل الدخول بالبصمة، يرجى إدخال بيانات تسجيل الدخول الخاصة بك:';

  @override
  String get enable => 'تفعيل';

  @override
  String get languageChanged => 'تم تغيير اللغة بنجاح';

  @override
  String get languageChangeError => 'حدث خطأ أثناء تغيير اللغة';

  @override
  String get restartRequired => 'يرجى إعادة تشغيل التطبيق لتطبيق التغييرات';

  @override
  String get success => 'تم بنجاح';

  @override
  String get error => 'خطأ';

  @override
  String get ok => 'موافق';

  @override
  String get cancel => 'إلغاء';

  @override
  String get save => 'حفظ';

  @override
  String get loading => 'جاري التحميل...';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get importantInstructions => 'تعليمات مهمة';

  @override
  String get passwordRequirements => 'تأكد من أن كلمة المرور الجديدة قوية وآمنة. يجب أن تحتوي على 8 أحرف على الأقل.';

  @override
  String get validationRequired => 'هذا الحقل مطلوب';

  @override
  String get validationEmail => 'يرجى إدخال بريد إلكتروني صحيح';

  @override
  String get validationPasswordLength => 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';

  @override
  String get validationPasswordMatch => 'كلمة المرور غير متطابقة';

  @override
  String get enterCurrentPassword => 'أدخل كلمة المرور الحالية';

  @override
  String get enterNewPassword => 'أدخل كلمة المرور الجديدة';

  @override
  String get confirmPassword => 'أعد إدخال كلمة المرور الجديدة';

  @override
  String get enterEmail => 'أدخل البريد الإلكتروني';

  @override
  String get enterPassword => 'أدخل كلمة المرور';

  @override
  String get connectionType1 => 'عقد اختبار';

  @override
  String get connectionType2 => 'عقد متعاون';

  @override
  String get connectionType3 => 'تعيين';

  @override
  String get connectionType4 => 'معار';

  @override
  String get connectionType5 => 'نذب';

  @override
  String get connectionType6 => 'نقل';
}
