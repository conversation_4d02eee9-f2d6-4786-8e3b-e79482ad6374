import 'package:flutter/material.dart';
import '../generated/l10n/app_localizations.dart';

/// نموذج بيانات الموظف
class Employee {
  final int id;
  final String name;
  final String? nationalNumber; // الرقم الوطني
  final String? connectedWithComp; // نوع الارتباط بالمؤسسة
  final String? managerName;
  final int? managerId;
  final int? resourceCalendarId; // معرف جدول العمل
  final String? image1920; // صورة الموظف بصيغة base64

  Employee({
    required this.id,
    required this.name,
    this.nationalNumber,
    this.connectedWithComp,
    this.managerName,
    this.managerId,
    this.resourceCalendarId,
    this.image1920,
  });

  /// الحصول على نص نوع الارتباط بالمؤسسة مترجم حسب لغة التطبيق
  String? getConnectionTypeText(BuildContext context) {
    if (connectedWithComp == null) return null;

    final localizations = AppLocalizations.of(context);
    if (localizations == null) return connectedWithComp;

    switch (connectedWithComp) {
      case '1':
        return localizations.connectionType1;
      case '2':
        return localizations.connectionType2;
      case '3':
        return localizations.connectionType3;
      case '4':
        return localizations.connectionType4;
      case '5':
        return localizations.connectionType5;
      case '6':
        return localizations.connectionType6;
      default:
        return connectedWithComp; // إرجاع القيمة الأصلية إذا لم تكن معرفة
    }
  }

  /// إنشاء كائن Employee من البيانات المستلمة من Odoo
  factory Employee.fromOdooData(Map<String, dynamic> data) {
    // استخراج اسم المدير من parent_id إذا كان موجود
    String? managerName;
    int? managerId;

    if (data['parent_id'] != null && data['parent_id'] is List) {
      final parentData = data['parent_id'] as List;
      if (parentData.length >= 2) {
        managerId = parentData[0] as int;
        managerName = parentData[1] as String;
      }
    }

    // تحويل connected_with_comp إلى نص إذا كان رقماً
    String? connectedWithComp;
    if (data['connected_with_comp'] != null) {
      connectedWithComp = data['connected_with_comp'].toString();
    }

    // استخراج معرف جدول العمل
    int? resourceCalendarId;
    if (data['resource_calendar_id'] != null &&
        data['resource_calendar_id'] != false) {
      if (data['resource_calendar_id'] is List) {
        final calendarData = data['resource_calendar_id'] as List;
        resourceCalendarId = calendarData.isNotEmpty
            ? calendarData[0] as int
            : null;
      } else if (data['resource_calendar_id'] is int) {
        resourceCalendarId = data['resource_calendar_id'] as int;
      }
    }

    // معالجة حقل الصورة بشكل آمن
    String? image1920;
    final imageData = data['image_1920'];
    if (imageData != null &&
        imageData != false &&
        imageData is String &&
        imageData.isNotEmpty) {
      image1920 = imageData;
    }

    return Employee(
      id: data['id'] as int,
      name: data['name'] as String,
      nationalNumber: data['national_number'] as String?,
      connectedWithComp: connectedWithComp,
      managerName: managerName,
      managerId: managerId,
      resourceCalendarId: resourceCalendarId,
      image1920: image1920,
    );
  }

  /// تحويل الكائن إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'nationalNumber': nationalNumber,
      'connectedWithComp': connectedWithComp,
      'managerName': managerName,
      'managerId': managerId,
      'resourceCalendarId': resourceCalendarId,
      'image_1920': image1920,
    };
  }

  @override
  String toString() {
    return 'Employee{id: $id, name: $name, nationalNumber: $nationalNumber, connectedWithComp: $connectedWithComp, managerName: $managerName, managerId: $managerId, resourceCalendarId: $resourceCalendarId}';
  }
}
