// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appName => 'Banque des Employés';

  @override
  String get appSubtitle => 'Système de Gestion des Employés Bancaires';

  @override
  String get loginTitle => 'Connexion';

  @override
  String get email => 'Email';

  @override
  String get password => 'Mot de passe';

  @override
  String get login => 'Se connecter';

  @override
  String get loginWithBiometric => 'Connexion biométrique';

  @override
  String get rememberMe => 'Se souvenir de moi';

  @override
  String get uncheckrememberme => 'Désélectionnez \'Se souvenir de moi\' car la biométrie est activée';

  @override
  String get enteremail => 'Entrez l\'email';

  @override
  String get validemail => 'Veuillez entrer un email valide';

  @override
  String get enterpassword => 'Entrez le mot de passe';

  @override
  String get correctpassword => 'Le mot de passe doit contenir au moins 6 caractères';

  @override
  String get securityInformation => 'Informations de sécurité';

  @override
  String get rememberMeInfo => 'Lorsque vous activez cette option, vos identifiants de connexion seront stockés de manière sécurisée sur votre appareil pour faciliter les connexions futures.\n\nVous pouvez désactiver cette option à tout moment dans les paramètres de l\'application.';

  @override
  String get understood => 'Compris';

  @override
  String get loginError => 'Erreur de connexion';

  @override
  String get invalidCredentials => 'Identifiants de connexion invalides';

  @override
  String get employeeInfo => 'Informations de l\'employé';

  @override
  String get nationalNumber => 'Numéro national';

  @override
  String get connectedWithCompany => 'Connecté à l\'entreprise';

  @override
  String get managerName => 'Nom du manager';

  @override
  String get workSchedule => 'Horaire de travail';

  @override
  String get refreshData => 'Actualiser les données';

  @override
  String get hiring => 'Recrutement';

  @override
  String get identification => 'Identification';

  @override
  String get directmanager => 'Manager direct';

  @override
  String get quickactions => 'Actions rapides';

  @override
  String get myprofile => 'Mon profil';

  @override
  String get leaveTypes => 'Types de congés';

  @override
  String get myLeaveRequests => 'Mes demandes';

  @override
  String get leaveApprovals => 'Approbations';

  @override
  String get settings => 'Paramètres';

  @override
  String get appSettings => 'Paramètres de l\'application';

  @override
  String get customizeSettings => 'Personnalisez les paramètres de l\'application selon vos besoins';

  @override
  String get accountSettings => 'Paramètres du compte';

  @override
  String get changePassword => 'Changer le mot de passe';

  @override
  String get changePasswordSubtitle => 'Mettez à jour votre mot de passe';

  @override
  String get securitySettings => 'Paramètres de sécurité';

  @override
  String get biometricLogin => 'Connexion biométrique';

  @override
  String get biometricEnabledSubtitle => 'Activé - Vous pouvez vous connecter avec la biométrie';

  @override
  String get biometricDisabledSubtitle => 'Désactivé - Utilisez uniquement le mot de passe';

  @override
  String get biometricUpdating => 'Mise à jour...';

  @override
  String get appearanceSettings => 'Paramètres d\'apparence';

  @override
  String get changeLanguage => 'Changer la langue';

  @override
  String get changeLanguageSubtitle => 'Choisissez votre langue préférée pour l\'application';

  @override
  String get selectLanguage => 'Sélectionner la langue';

  @override
  String get languages => 'Langues';

  @override
  String get arabic => 'Arabe';

  @override
  String get english => 'Anglais';

  @override
  String get french => 'Français';

  @override
  String get currentPassword => 'Mot de passe actuel';

  @override
  String get newPassword => 'Nouveau mot de passe';

  @override
  String get confirmNewPassword => 'Confirmer le nouveau mot de passe';

  @override
  String get updatePassword => 'Mettre à jour le mot de passe';

  @override
  String get passwordUpdated => 'Mot de passe mis à jour avec succès';

  @override
  String get passwordUpdateFailed => 'Échec de la mise à jour du mot de passe';

  @override
  String get biometricEnabled => 'Connexion biométrique activée avec succès';

  @override
  String get biometricDisabled => 'Connexion biométrique désactivée avec succès';

  @override
  String get biometricError => 'Erreur lors de la mise à jour des paramètres biométriques';

  @override
  String get biometricSetup => 'Configuration de la connexion biométrique';

  @override
  String get biometricSetupMessage => 'Pour activer la connexion biométrique, veuillez saisir vos identifiants de connexion:';

  @override
  String get enable => 'Activer';

  @override
  String get languageChanged => 'Langue changée avec succès';

  @override
  String get languageChangeError => 'Erreur lors du changement de langue';

  @override
  String get restartRequired => 'Veuillez redémarrer l\'application pour appliquer les modifications';

  @override
  String get success => 'Succès';

  @override
  String get error => 'Erreur';

  @override
  String get ok => 'OK';

  @override
  String get cancel => 'Annuler';

  @override
  String get save => 'Enregistrer';

  @override
  String get loading => 'Chargement...';

  @override
  String get retry => 'Réessayer';

  @override
  String get importantInstructions => 'Instructions importantes';

  @override
  String get passwordRequirements => 'Assurez-vous que votre nouveau mot de passe est fort et sécurisé. Il doit contenir au moins 8 caractères.';

  @override
  String get validationRequired => 'Ce champ est requis';

  @override
  String get validationEmail => 'Veuillez entrer un email valide';

  @override
  String get validationPasswordLength => 'Le mot de passe doit contenir au moins 8 caractères';

  @override
  String get validationPasswordMatch => 'Les mots de passe ne correspondent pas';

  @override
  String get enterCurrentPassword => 'Entrez le mot de passe actuel';

  @override
  String get enterNewPassword => 'Entrez le nouveau mot de passe';

  @override
  String get confirmPassword => 'Ressaisissez le nouveau mot de passe';

  @override
  String get enterEmail => 'Entrez l\'email';

  @override
  String get enterPassword => 'Entrez le mot de passe';
}
