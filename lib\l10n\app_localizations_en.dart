// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Employee Bank';

  @override
  String get appSubtitle => 'Banking Employee Management System';

  @override
  String get loginTitle => 'Login';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get login => 'Login';

  @override
  String get loginWithBiometric => 'Login with Biometric';

  @override
  String get rememberMe => 'Remember Me';

  @override
  String get uncheckrememberme => 'Uncheck \'Remember Me\' because biometric is enabled';

  @override
  String get enteremail => 'Enter email';

  @override
  String get validemail => 'Please enter a valid email';

  @override
  String get enterpassword => 'Enter password';

  @override
  String get correctpassword => 'Password must be at least 6 characters';

  @override
  String get securityInformation => 'Security Information';

  @override
  String get rememberMeInfo => 'When you enable this option, your login credentials will be securely stored on your device to make future logins easier.\n\nYou can disable this option at any time from the app settings.';

  @override
  String get understood => 'Understood';

  @override
  String get loginError => 'Login error occurred';

  @override
  String get invalidCredentials => 'Invalid login credentials';

  @override
  String get employeeInfo => 'Employee Information';

  @override
  String get nationalNumber => 'National Number';

  @override
  String get connectedWithCompany => 'Connected with Company';

  @override
  String get managerName => 'Manager Name';

  @override
  String get workSchedule => 'Work Schedule';

  @override
  String get refreshData => 'Refresh Data';

  @override
  String get hiring => 'Hiring';

  @override
  String get identification => 'Identification';

  @override
  String get directmanager => 'Direct Manager';

  @override
  String get quickactions => 'Quick Actions';

  @override
  String get myprofile => 'My Profile';

  @override
  String get leaveTypes => 'Leave Types';

  @override
  String get myLeaveRequests => 'My Requests';

  @override
  String get leaveApprovals => 'Approvals';

  @override
  String get settings => 'Settings';

  @override
  String get appSettings => 'App Settings';

  @override
  String get customizeSettings => 'Customize app settings according to your needs';

  @override
  String get accountSettings => 'Account Settings';

  @override
  String get changePassword => 'Change Password';

  @override
  String get changePasswordSubtitle => 'Update your password';

  @override
  String get securitySettings => 'Security Settings';

  @override
  String get biometricLogin => 'Biometric Login';

  @override
  String get biometricEnabledSubtitle => 'Enabled - You can login with biometric';

  @override
  String get biometricDisabledSubtitle => 'Disabled - Use password only';

  @override
  String get biometricUpdating => 'Updating...';

  @override
  String get appearanceSettings => 'Appearance Settings';

  @override
  String get changeLanguage => 'Change Language';

  @override
  String get changeLanguageSubtitle => 'Choose your preferred app language';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get languages => 'Languages';

  @override
  String get arabic => 'Arabic';

  @override
  String get english => 'English';

  @override
  String get french => 'French';

  @override
  String get currentPassword => 'Current Password';

  @override
  String get newPassword => 'New Password';

  @override
  String get confirmNewPassword => 'Confirm New Password';

  @override
  String get updatePassword => 'Update Password';

  @override
  String get passwordUpdated => 'Password updated successfully';

  @override
  String get passwordUpdateFailed => 'Failed to update password';

  @override
  String get biometricEnabled => 'Biometric login enabled successfully';

  @override
  String get biometricDisabled => 'Biometric login disabled successfully';

  @override
  String get biometricError => 'Error occurred while updating biometric settings';

  @override
  String get biometricSetup => 'Setup Biometric Login';

  @override
  String get biometricSetupMessage => 'To enable biometric login, please enter your login credentials:';

  @override
  String get enable => 'Enable';

  @override
  String get languageChanged => 'Language changed successfully';

  @override
  String get languageChangeError => 'Error occurred while changing language';

  @override
  String get restartRequired => 'Please restart the app to apply changes';

  @override
  String get success => 'Success';

  @override
  String get error => 'Error';

  @override
  String get ok => 'OK';

  @override
  String get cancel => 'Cancel';

  @override
  String get save => 'Save';

  @override
  String get loading => 'Loading...';

  @override
  String get retry => 'Retry';

  @override
  String get importantInstructions => 'Important Instructions';

  @override
  String get passwordRequirements => 'Make sure your new password is strong and secure. It should contain at least 8 characters.';

  @override
  String get validationRequired => 'This field is required';

  @override
  String get validationEmail => 'Please enter a valid email';

  @override
  String get validationPasswordLength => 'Password must be at least 8 characters';

  @override
  String get validationPasswordMatch => 'Passwords do not match';

  @override
  String get enterCurrentPassword => 'Enter current password';

  @override
  String get enterNewPassword => 'Enter new password';

  @override
  String get confirmPassword => 'Re-enter new password';

  @override
  String get enterEmail => 'Enter email';

  @override
  String get enterPassword => 'Enter password';

  @override
  String get connectionType1 => 'Trial Contract';

  @override
  String get connectionType2 => 'Collaborator Contract';

  @override
  String get connectionType3 => 'Appointment';

  @override
  String get connectionType4 => 'Seconded';

  @override
  String get connectionType5 => 'Delegated';

  @override
  String get connectionType6 => 'Transfer';
}
