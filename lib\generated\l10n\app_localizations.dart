import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';
import 'app_localizations_fr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
    Locale('fr'),
  ];

  /// No description provided for @appName.
  ///
  /// In ar, this message translates to:
  /// **'بنك الموظفين'**
  String get appName;

  /// No description provided for @appSubtitle.
  ///
  /// In ar, this message translates to:
  /// **'نظام إدارة الموظفين المصرفي'**
  String get appSubtitle;

  /// No description provided for @loginTitle.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الدخول'**
  String get loginTitle;

  /// No description provided for @email.
  ///
  /// In ar, this message translates to:
  /// **'البريد الإلكتروني'**
  String get email;

  /// No description provided for @password.
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور'**
  String get password;

  /// No description provided for @login.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الدخول'**
  String get login;

  /// No description provided for @loginWithBiometric.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الدخول بالبصمة'**
  String get loginWithBiometric;

  /// No description provided for @rememberMe.
  ///
  /// In ar, this message translates to:
  /// **'تذكرني'**
  String get rememberMe;

  /// No description provided for @uncheckrememberme.
  ///
  /// In ar, this message translates to:
  /// **'تم إلغاء تذكر بيانات تسجيل الدخول لأن البصمة مفعلة '**
  String get uncheckrememberme;

  /// No description provided for @enteremail.
  ///
  /// In ar, this message translates to:
  /// **'أدخل البريد الإلكتروني'**
  String get enteremail;

  /// No description provided for @validemail.
  ///
  /// In ar, this message translates to:
  /// **'يرجى إدخال بريد إلكتروني صحيح'**
  String get validemail;

  /// No description provided for @enterpassword.
  ///
  /// In ar, this message translates to:
  /// **'أدخل كلمة المرور'**
  String get enterpassword;

  /// No description provided for @passwordLength.
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور يجب أن تكون 6 أحرف على الأقل'**
  String get correctpassword;

  /// No description provided for @securityInformation.
  ///
  /// In ar, this message translates to:
  /// **'معلومات الأمان'**
  String get securityInformation;

  /// No description provided for @rememberMeInfo.
  ///
  /// In ar, this message translates to:
  /// **'عند تفعيل هذا الخيار، سيتم حفظ بيانات تسجيل الدخول بشكل آمن على جهازك لتسهيل الدخول في المرات القادمة.\n\nيمكنك إلغاء تفعيل هذا الخيار في أي وقت من إعدادات التطبيق.'**
  String get rememberMeInfo;

  /// No description provided for @understood.
  ///
  /// In ar, this message translates to:
  /// **'فهمت'**
  String get understood;

  /// No description provided for @loginError.
  ///
  /// In ar, this message translates to:
  /// **'حدث خطأ في تسجيل الدخول'**
  String get loginError;

  /// No description provided for @invalidCredentials.
  ///
  /// In ar, this message translates to:
  /// **'بيانات تسجيل الدخول غير صحيحة'**
  String get invalidCredentials;

  /// No description provided for @employeeInfo.
  ///
  /// In ar, this message translates to:
  /// **'معلومات الموظف'**
  String get employeeInfo;

  /// No description provided for @nationalNumber.
  ///
  /// In ar, this message translates to:
  /// **'الرقم الوطني'**
  String get nationalNumber;

  /// No description provided for @connectedWithCompany.
  ///
  /// In ar, this message translates to:
  /// **'مرتبط بالشركة'**
  String get connectedWithCompany;

  /// No description provided for @managerName.
  ///
  /// In ar, this message translates to:
  /// **'اسم المدير'**
  String get managerName;

  /// No description provided for @workSchedule.
  ///
  /// In ar, this message translates to:
  /// **'جدول العمل'**
  String get workSchedule;

  /// No description provided for @refreshData.
  ///
  /// In ar, this message translates to:
  /// **'تحديث البيانات'**
  String get refreshData;

  /// No description provided for @hiring.
  ///
  /// In ar, this message translates to:
  /// **'تعيين'**
  String get hiring;

  /// No description provided for @identification.
  ///
  /// In ar, this message translates to:
  /// **'الرقم الوطني'**
  String get identification;

  /// No description provided for @directmanager.
  ///
  /// In ar, this message translates to:
  /// **'المدير المباشر'**
  String get directmanager;

  /// No description provided for @quickactions.
  ///
  /// In ar, this message translates to:
  /// **'إجراءات سريعة'**
  String get quickactions;

  /// No description provided for @myprofile.
  ///
  /// In ar, this message translates to:
  /// **'الملف الشخصي'**
  String get myprofile;

  /// No description provided for @leaveTypes.
  ///
  /// In ar, this message translates to:
  /// **'أنواع الإجازات'**
  String get leaveTypes;

  /// No description provided for @myLeaveRequests.
  ///
  /// In ar, this message translates to:
  /// **'طلباتي'**
  String get myLeaveRequests;

  /// No description provided for @leaveApprovals.
  ///
  /// In ar, this message translates to:
  /// **'الموافقات'**
  String get leaveApprovals;

  /// No description provided for @settings.
  ///
  /// In ar, this message translates to:
  /// **'الإعدادات'**
  String get settings;

  /// No description provided for @appSettings.
  ///
  /// In ar, this message translates to:
  /// **'إعدادات التطبيق'**
  String get appSettings;

  /// No description provided for @customizeSettings.
  ///
  /// In ar, this message translates to:
  /// **'قم بتخصيص إعدادات التطبيق حسب احتياجاتك'**
  String get customizeSettings;

  /// No description provided for @accountSettings.
  ///
  /// In ar, this message translates to:
  /// **'إعدادات الحساب'**
  String get accountSettings;

  /// No description provided for @changePassword.
  ///
  /// In ar, this message translates to:
  /// **'تغيير كلمة المرور'**
  String get changePassword;

  /// No description provided for @changePasswordSubtitle.
  ///
  /// In ar, this message translates to:
  /// **'قم بتحديث كلمة المرور الخاصة بك'**
  String get changePasswordSubtitle;

  /// No description provided for @securitySettings.
  ///
  /// In ar, this message translates to:
  /// **'إعدادات الأمان'**
  String get securitySettings;

  /// No description provided for @biometricLogin.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الدخول بالبصمة'**
  String get biometricLogin;

  /// No description provided for @biometricEnabledSubtitle.
  ///
  /// In ar, this message translates to:
  /// **'مفعل - يمكنك تسجيل الدخول بالبصمة'**
  String get biometricEnabledSubtitle;

  /// No description provided for @biometricDisabledSubtitle.
  ///
  /// In ar, this message translates to:
  /// **'غير مفعل - استخدم كلمة المرور فقط'**
  String get biometricDisabledSubtitle;

  /// No description provided for @biometricUpdating.
  ///
  /// In ar, this message translates to:
  /// **'جاري التحديث...'**
  String get biometricUpdating;

  /// No description provided for @appearanceSettings.
  ///
  /// In ar, this message translates to:
  /// **'إعدادات المظهر'**
  String get appearanceSettings;

  /// No description provided for @changeLanguage.
  ///
  /// In ar, this message translates to:
  /// **'تغيير اللغة'**
  String get changeLanguage;

  /// No description provided for @changeLanguageSubtitle.
  ///
  /// In ar, this message translates to:
  /// **'اختر لغة التطبيق المفضلة لديك'**
  String get changeLanguageSubtitle;

  /// No description provided for @selectLanguage.
  ///
  /// In ar, this message translates to:
  /// **'اختر اللغة'**
  String get selectLanguage;

  /// No description provided for @languages.
  ///
  /// In ar, this message translates to:
  /// **'اللغات'**
  String get languages;

  /// No description provided for @arabic.
  ///
  /// In ar, this message translates to:
  /// **'العربية'**
  String get arabic;

  /// No description provided for @english.
  ///
  /// In ar, this message translates to:
  /// **'الإنجليزية'**
  String get english;

  /// No description provided for @french.
  ///
  /// In ar, this message translates to:
  /// **'الفرنسية'**
  String get french;

  /// No description provided for @currentPassword.
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور الحالية'**
  String get currentPassword;

  /// No description provided for @newPassword.
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور الجديدة'**
  String get newPassword;

  /// No description provided for @confirmNewPassword.
  ///
  /// In ar, this message translates to:
  /// **'تأكيد كلمة المرور الجديدة'**
  String get confirmNewPassword;

  /// No description provided for @updatePassword.
  ///
  /// In ar, this message translates to:
  /// **'تحديث كلمة المرور'**
  String get updatePassword;

  /// No description provided for @passwordUpdated.
  ///
  /// In ar, this message translates to:
  /// **'تم تحديث كلمة المرور بنجاح'**
  String get passwordUpdated;

  /// No description provided for @passwordUpdateFailed.
  ///
  /// In ar, this message translates to:
  /// **'فشل في تحديث كلمة المرور'**
  String get passwordUpdateFailed;

  /// No description provided for @biometricEnabled.
  ///
  /// In ar, this message translates to:
  /// **'تم تفعيل تسجيل الدخول بالبصمة بنجاح'**
  String get biometricEnabled;

  /// No description provided for @biometricDisabled.
  ///
  /// In ar, this message translates to:
  /// **'تم إلغاء تفعيل تسجيل الدخول بالبصمة'**
  String get biometricDisabled;

  /// No description provided for @biometricError.
  ///
  /// In ar, this message translates to:
  /// **'حدث خطأ أثناء تحديث إعدادات البصمة'**
  String get biometricError;

  /// No description provided for @biometricSetup.
  ///
  /// In ar, this message translates to:
  /// **'إعداد تسجيل الدخول بالبصمة'**
  String get biometricSetup;

  /// No description provided for @biometricSetupMessage.
  ///
  /// In ar, this message translates to:
  /// **'لتفعيل تسجيل الدخول بالبصمة، يرجى إدخال بيانات تسجيل الدخول الخاصة بك:'**
  String get biometricSetupMessage;

  /// No description provided for @enable.
  ///
  /// In ar, this message translates to:
  /// **'تفعيل'**
  String get enable;

  /// No description provided for @languageChanged.
  ///
  /// In ar, this message translates to:
  /// **'تم تغيير اللغة بنجاح'**
  String get languageChanged;

  /// No description provided for @languageChangeError.
  ///
  /// In ar, this message translates to:
  /// **'حدث خطأ أثناء تغيير اللغة'**
  String get languageChangeError;

  /// No description provided for @restartRequired.
  ///
  /// In ar, this message translates to:
  /// **'يرجى إعادة تشغيل التطبيق لتطبيق التغييرات'**
  String get restartRequired;

  /// No description provided for @success.
  ///
  /// In ar, this message translates to:
  /// **'تم بنجاح'**
  String get success;

  /// No description provided for @error.
  ///
  /// In ar, this message translates to:
  /// **'خطأ'**
  String get error;

  /// No description provided for @ok.
  ///
  /// In ar, this message translates to:
  /// **'موافق'**
  String get ok;

  /// No description provided for @cancel.
  ///
  /// In ar, this message translates to:
  /// **'إلغاء'**
  String get cancel;

  /// No description provided for @save.
  ///
  /// In ar, this message translates to:
  /// **'حفظ'**
  String get save;

  /// No description provided for @loading.
  ///
  /// In ar, this message translates to:
  /// **'جاري التحميل...'**
  String get loading;

  /// No description provided for @retry.
  ///
  /// In ar, this message translates to:
  /// **'إعادة المحاولة'**
  String get retry;

  /// No description provided for @importantInstructions.
  ///
  /// In ar, this message translates to:
  /// **'تعليمات مهمة'**
  String get importantInstructions;

  /// No description provided for @passwordRequirements.
  ///
  /// In ar, this message translates to:
  /// **'تأكد من أن كلمة المرور الجديدة قوية وآمنة. يجب أن تحتوي على 8 أحرف على الأقل.'**
  String get passwordRequirements;

  /// No description provided for @validationRequired.
  ///
  /// In ar, this message translates to:
  /// **'هذا الحقل مطلوب'**
  String get validationRequired;

  /// No description provided for @validationEmail.
  ///
  /// In ar, this message translates to:
  /// **'يرجى إدخال بريد إلكتروني صحيح'**
  String get validationEmail;

  /// No description provided for @validationPasswordLength.
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور يجب أن تكون 8 أحرف على الأقل'**
  String get validationPasswordLength;

  /// No description provided for @validationPasswordMatch.
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور غير متطابقة'**
  String get validationPasswordMatch;

  /// No description provided for @enterCurrentPassword.
  ///
  /// In ar, this message translates to:
  /// **'أدخل كلمة المرور الحالية'**
  String get enterCurrentPassword;

  /// No description provided for @enterNewPassword.
  ///
  /// In ar, this message translates to:
  /// **'أدخل كلمة المرور الجديدة'**
  String get enterNewPassword;

  /// No description provided for @confirmPassword.
  ///
  /// In ar, this message translates to:
  /// **'أعد إدخال كلمة المرور الجديدة'**
  String get confirmPassword;

  /// No description provided for @enterEmail.
  ///
  /// In ar, this message translates to:
  /// **'أدخل البريد الإلكتروني'**
  String get enterEmail;

  /// No description provided for @enterPassword.
  ///
  /// In ar, this message translates to:
  /// **'أدخل كلمة المرور'**
  String get enterPassword;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en', 'fr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
    case 'fr':
      return AppLocalizationsFr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
